<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Table Demo - Vertigo AMS</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/vue-good-table-next@0.1.7/dist/vue-good-table-next.umd.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/vue-good-table-next@0.1.7/dist/vue-good-table-next.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Enhanced Vue Good Table Styles */
        .vgt-table {
            width: 100%;
            background-color: white;
            color: #374151 !important;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .vgt-table thead th {
            text-align: left;
            font-size: 0.75rem;
            font-weight: 600;
            color: #4b5563 !important;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 1rem 1.5rem;
            border-bottom: 2px solid #e5e7eb !important;
            background: linear-gradient(to right, #f9fafb, #f3f4f6) !important;
            transition: all 0.2s ease;
        }

        .vgt-table thead th.sortable {
            cursor: pointer;
            user-select: none;
        }

        .vgt-table thead th.sortable:hover {
            background: linear-gradient(to right, #f3f4f6, #e5e7eb) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .vgt-table tbody td {
            padding: 1rem 1.5rem;
            font-size: 0.875rem;
            color: #111827 !important;
            border-bottom: 1px solid #f3f4f6 !important;
            background-color: white !important;
            transition: all 0.15s ease;
        }

        .vgt-table tbody tr {
            background-color: white !important;
            transition: all 0.15s ease;
        }

        .vgt-table tbody tr:hover {
            background: linear-gradient(to right, #eff6ff, #eef2ff) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
        }

        .vgt-table tbody tr:hover td {
            background: transparent !important;
            color: #1e40af !important;
        }

        .vgt-table.striped tbody tr:nth-child(even) {
            background-color: #fafafa !important;
        }

        .vgt-table.striped tbody tr:nth-child(even):hover {
            background: linear-gradient(to right, #eff6ff, #eef2ff) !important;
        }

        .vgt-table .vgt-pagination {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            background: linear-gradient(to right, #f9fafb, #f3f4f6) !important;
            border-top: 1px solid #e5e7eb !important;
        }

        .vgt-table .vgt-pagination button {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid #d1d5db !important;
            border-radius: 0.375rem;
            background-color: white !important;
            color: #374151 !important;
            transition: all 0.15s ease;
            min-width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .vgt-table .vgt-pagination button:hover:not(:disabled) {
            background-color: #f9fafb !important;
            border-color: #9ca3af !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
        }

        .vgt-table .vgt-pagination button.active {
            background-color: #3b82f6 !important;
            color: white !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-completed { background-color: #dcfce7; color: #166534; }
        .status-pending { background-color: #fef3c7; color: #92400e; }
        .status-processing { background-color: #dbeafe; color: #1e40af; }
        .status-cancelled { background-color: #fee2e2; color: #dc2626; }

        .customer-cell {
            display: flex;
            align-items: center;
        }

        .customer-avatar {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            margin-right: 0.75rem;
        }

        .customer-info h4 {
            font-weight: 500;
            color: #111827;
            margin: 0;
        }

        .customer-info p {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .search-container {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            width: 1rem;
            height: 1rem;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen py-8">
    <div id="app">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Enhanced Table Demo</h1>
                <p class="text-gray-600">Showcase of the enhanced Vue Good Table with improved styling and functionality</p>
            </div>

            <!-- Enhanced Table -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <!-- Table Header -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Sample Orders</h3>
                            <p class="text-sm text-gray-600 mt-1">Manage your orders with enhanced table features</p>
                        </div>
                        <div class="flex gap-3">
                            <button class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Export
                            </button>
                            <button class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Create Order
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Search -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="search-container">
                        <svg class="search-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <input
                            v-model="searchQuery"
                            type="text"
                            placeholder="Search orders..."
                            class="search-input"
                        />
                    </div>
                </div>

                <!-- Vue Good Table -->
                <vue-good-table
                    :columns="columns"
                    :rows="filteredRows"
                    :pagination-options="paginationOptions"
                    :search-options="searchOptions"
                    :sort-options="sortOptions"
                    styleClass="vgt-table striped"
                >
                    <template #table-row="props">
                        <span v-if="props.column.field === 'customer'">
                            <div class="customer-cell">
                                <div class="customer-avatar">
                                    {{ getInitials(props.row.customer) }}
                                </div>
                                <div class="customer-info">
                                    <h4>{{ props.row.customer }}</h4>
                                    <p>{{ props.row.email }}</p>
                                </div>
                            </div>
                        </span>
                        <span v-else-if="props.column.field === 'status'">
                            <span :class="['status-badge', `status-${props.row.status}`]">
                                {{ props.row.status }}
                            </span>
                        </span>
                        <span v-else-if="props.column.field === 'amount'">
                            <span class="font-semibold text-gray-900">
                                ${{ props.row.amount.toLocaleString('en-US', { minimumFractionDigits: 2 }) }}
                            </span>
                        </span>
                        <span v-else-if="props.column.field === 'created_at'">
                            {{ formatDate(props.row.created_at) }}
                        </span>
                        <span v-else>
                            {{ props.formattedRow[props.column.field] }}
                        </span>
                    </template>
                </vue-good-table>
            </div>

            <!-- Statistics -->
            <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 bg-blue-500 rounded-md flex items-center justify-center">
                                    <span class="text-white font-semibold text-sm">{{ rows.length }}</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ rows.length }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 bg-green-500 rounded-md flex items-center justify-center">
                                    <span class="text-white font-semibold text-sm">{{ completedCount }}</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ completedCount }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                    <span class="text-white font-semibold text-sm">{{ pendingCount }}</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ pendingCount }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
                                    <span class="text-white font-semibold text-sm">${{ totalRevenue.toLocaleString() }}</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Revenue</dt>
                                    <dd class="text-lg font-medium text-gray-900">${{ totalRevenue.toLocaleString() }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { VueGoodTable } = window.VueGoodTableNext;

        createApp({
            components: {
                VueGoodTable
            },
            data() {
                return {
                    searchQuery: '',
                    columns: [
                        {
                            label: 'Order ID',
                            field: 'order_id',
                            sortable: true,
                            width: '120px'
                        },
                        {
                            label: 'Customer',
                            field: 'customer',
                            sortable: true,
                            width: '280px'
                        },
                        {
                            label: 'Amount',
                            field: 'amount',
                            sortable: true,
                            type: 'number',
                            width: '120px'
                        },
                        {
                            label: 'Status',
                            field: 'status',
                            sortable: true,
                            width: '120px'
                        },
                        {
                            label: 'Items',
                            field: 'items',
                            sortable: true,
                            type: 'number',
                            width: '80px'
                        },
                        {
                            label: 'Date',
                            field: 'created_at',
                            sortable: true,
                            type: 'date',
                            width: '140px'
                        }
                    ],
                    rows: [
                        {
                            id: 1,
                            order_id: 'ORD-001',
                            customer: 'John Doe',
                            email: '<EMAIL>',
                            amount: 1250.00,
                            status: 'completed',
                            created_at: '2024-01-15T10:30:00Z',
                            items: 3
                        },
                        {
                            id: 2,
                            order_id: 'ORD-002',
                            customer: 'Jane Smith',
                            email: '<EMAIL>',
                            amount: 875.50,
                            status: 'pending',
                            created_at: '2024-01-14T14:20:00Z',
                            items: 2
                        },
                        {
                            id: 3,
                            order_id: 'ORD-003',
                            customer: 'Bob Johnson',
                            email: '<EMAIL>',
                            amount: 2100.75,
                            status: 'processing',
                            created_at: '2024-01-13T09:15:00Z',
                            items: 5
                        },
                        {
                            id: 4,
                            order_id: 'ORD-004',
                            customer: 'Alice Brown',
                            email: '<EMAIL>',
                            amount: 450.25,
                            status: 'cancelled',
                            created_at: '2024-01-12T16:45:00Z',
                            items: 1
                        },
                        {
                            id: 5,
                            order_id: 'ORD-005',
                            customer: 'Charlie Wilson',
                            email: '<EMAIL>',
                            amount: 1875.00,
                            status: 'completed',
                            created_at: '2024-01-11T11:30:00Z',
                            items: 4
                        },
                        {
                            id: 6,
                            order_id: 'ORD-006',
                            customer: 'Diana Prince',
                            email: '<EMAIL>',
                            amount: 3200.00,
                            status: 'completed',
                            created_at: '2024-01-10T08:45:00Z',
                            items: 7
                        },
                        {
                            id: 7,
                            order_id: 'ORD-007',
                            customer: 'Clark Kent',
                            email: '<EMAIL>',
                            amount: 675.25,
                            status: 'processing',
                            created_at: '2024-01-09T15:20:00Z',
                            items: 2
                        },
                        {
                            id: 8,
                            order_id: 'ORD-008',
                            customer: 'Bruce Wayne',
                            email: '<EMAIL>',
                            amount: 5500.00,
                            status: 'completed',
                            created_at: '2024-01-08T12:10:00Z',
                            items: 12
                        }
                    ],
                    paginationOptions: {
                        enabled: true,
                        mode: 'records',
                        perPage: 5,
                        perPageDropdown: [5, 10, 15, 20],
                        dropdownAllowAll: false,
                        nextLabel: 'Next',
                        prevLabel: 'Previous',
                        rowsPerPageLabel: 'Rows per page',
                        ofLabel: 'of',
                        pageLabel: 'page'
                    },
                    searchOptions: {
                        enabled: false
                    },
                    sortOptions: {
                        enabled: true,
                        multipleColumns: false
                    }
                };
            },
            computed: {
                filteredRows() {
                    if (!this.searchQuery.trim()) {
                        return this.rows;
                    }
                    
                    const query = this.searchQuery.toLowerCase();
                    return this.rows.filter(row => {
                        return Object.values(row).some(value => 
                            String(value).toLowerCase().includes(query)
                        );
                    });
                },
                completedCount() {
                    return this.rows.filter(row => row.status === 'completed').length;
                },
                pendingCount() {
                    return this.rows.filter(row => row.status === 'pending').length;
                },
                totalRevenue() {
                    return this.rows
                        .filter(row => row.status === 'completed')
                        .reduce((sum, row) => sum + row.amount, 0);
                }
            },
            methods: {
                getInitials(name) {
                    return name
                        .split(' ')
                        .map(word => word.charAt(0))
                        .join('')
                        .toUpperCase();
                },
                formatDate(dateString) {
                    return new Date(dateString).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
