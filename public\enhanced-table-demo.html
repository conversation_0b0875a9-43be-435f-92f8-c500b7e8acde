<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Table Demo - Vertigo AMS</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/vue-good-table-next@0.1.7/dist/vue-good-table-next.umd.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/vue-good-table-next@0.1.7/dist/vue-good-table-next.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Enhanced Vue Good Table Styles */
        .vgt-table {
            width: 100%;
            background-color: white;
            color: #374151 !important;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .vgt-table thead th {
            text-align: left;
            font-size: 0.75rem;
            font-weight: 600;
            color: #4b5563 !important;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 1rem 1.5rem;
            border-bottom: 2px solid #e5e7eb !important;
            background: linear-gradient(to right, #f9fafb, #f3f4f6) !important;
            transition: all 0.2s ease;
        }

        .vgt-table thead th.sortable {
            cursor: pointer;
            user-select: none;
        }

        .vgt-table thead th.sortable:hover {
            background: linear-gradient(to right, #f3f4f6, #e5e7eb) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .vgt-table tbody td {
            padding: 1rem 1.5rem;
            font-size: 0.875rem;
            color: #111827 !important;
            border-bottom: 1px solid #f3f4f6 !important;
            background-color: white !important;
            transition: all 0.15s ease;
        }

        .vgt-table tbody tr {
            background-color: white !important;
            transition: all 0.15s ease;
        }

        .vgt-table tbody tr:hover {
            background: linear-gradient(to right, #eff6ff, #eef2ff) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
        }

        .vgt-table tbody tr:hover td {
            background: transparent !important;
            color: #1e40af !important;
        }

        .vgt-table.striped tbody tr:nth-child(even) {
            background-color: #fafafa !important;
        }

        .vgt-table.striped tbody tr:nth-child(even):hover {
            background: linear-gradient(to right, #eff6ff, #eef2ff) !important;
        }

        .vgt-table .vgt-pagination {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            background: linear-gradient(to right, #f9fafb, #f3f4f6) !important;
            border-top: 1px solid #e5e7eb !important;
        }

        .vgt-table .vgt-pagination button {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            font-weight: 500;
            border: 1px solid #d1d5db !important;
            border-radius: 0.375rem;
            background-color: white !important;
            color: #374151 !important;
            transition: all 0.15s ease;
            min-width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .vgt-table .vgt-pagination button:hover:not(:disabled) {
            background-color: #f9fafb !important;
            border-color: #9ca3af !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
        }

        .vgt-table .vgt-pagination button.active {
            background-color: #3b82f6 !important;
            color: white !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-completed { background-color: #dcfce7; color: #166534; }
        .status-pending { background-color: #fef3c7; color: #92400e; }
        .status-processing { background-color: #dbeafe; color: #1e40af; }
        .status-cancelled { background-color: #fee2e2; color: #dc2626; }

        .customer-cell {
            display: flex;
            align-items: center;
        }

        .customer-avatar {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.875rem;
            margin-right: 0.75rem;
        }

        .customer-info h4 {
            font-weight: 500;
            color: #111827;
            margin: 0;
        }

        .customer-info p {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .search-container {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .search-input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            width: 1rem;
            height: 1rem;
        }

        /* Loading States */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .spinner {
            width: 2rem;
            height: 2rem;
            border: 3px solid #e5e7eb;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .skeleton-row {
            display: flex;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .skeleton-cell {
            height: 1rem;
            background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 0.25rem;
            margin-right: 1rem;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* Error States */
        .error-state {
            text-align: center;
            padding: 3rem 2rem;
            color: #6b7280;
        }

        .error-icon {
            width: 4rem;
            height: 4rem;
            margin: 0 auto 1rem;
            color: #ef4444;
        }

        .error-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 0.5rem;
        }

        .error-message {
            color: #6b7280;
            margin-bottom: 1.5rem;
        }

        .retry-button {
            background-color: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .retry-button:hover {
            background-color: #2563eb;
        }

        /* Empty States */
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #6b7280;
        }

        .empty-icon {
            width: 5rem;
            height: 5rem;
            margin: 0 auto 1.5rem;
            color: #d1d5db;
        }

        .empty-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 0.5rem;
        }

        .empty-message {
            color: #6b7280;
            margin-bottom: 2rem;
            max-width: 28rem;
            margin-left: auto;
            margin-right: auto;
        }

        .create-button {
            background-color: #3b82f6;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .create-button:hover {
            background-color: #2563eb;
        }

        /* Toast Notifications */
        .toast-container {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 50;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .toast {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            border-left: 4px solid;
            min-width: 20rem;
            animation: slideIn 0.3s ease-out;
        }

        .toast.success { border-left-color: #10b981; }
        .toast.error { border-left-color: #ef4444; }
        .toast.warning { border-left-color: #f59e0b; }
        .toast.info { border-left-color: #3b82f6; }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .toast-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.25rem;
        }

        .toast-title {
            font-weight: 600;
            color: #111827;
        }

        .toast-close {
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 1.25rem;
            height: 1.25rem;
        }

        .toast-message {
            color: #6b7280;
            font-size: 0.875rem;
        }

        /* Action buttons */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .action-button {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-button:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .action-button.primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .action-button.primary:hover {
            background: #2563eb;
        }

        .action-button.danger {
            background: #ef4444;
            color: white;
            border-color: #ef4444;
        }

        .action-button.danger:hover {
            background: #dc2626;
        }

        /* Refresh indicator */
        .refresh-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .refresh-spinner {
            width: 1rem;
            height: 1rem;
            border: 2px solid #e5e7eb;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen py-8">
    <div id="app">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Enhanced Table Demo</h1>
                <p class="text-gray-600">Showcase of the enhanced Vue Good Table with improved styling and functionality</p>
            </div>

            <!-- Enhanced Table -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <!-- Table Header -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Sample Orders</h3>
                            <p class="text-sm text-gray-600 mt-1">Manage your orders with enhanced table features</p>
                        </div>
                        <div class="flex gap-3">
                            <button class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Export
                            </button>
                            <button class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                                Create Order
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Search and Controls -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                        <div class="search-container flex-1 max-w-md">
                            <svg class="search-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <input
                                v-model="searchQuery"
                                type="text"
                                placeholder="Search orders..."
                                class="search-input"
                                :disabled="isLoading"
                            />
                        </div>
                        <div class="flex items-center gap-3">
                            <div v-if="isRefreshing" class="refresh-indicator">
                                <div class="refresh-spinner"></div>
                                Refreshing...
                            </div>
                            <button
                                @click="refreshData"
                                :disabled="isLoading || isRefreshing"
                                class="action-button"
                                title="Refresh data"
                            >
                                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                            </button>
                            <select
                                v-model="statusFilter"
                                class="action-button"
                                :disabled="isLoading"
                            >
                                <option value="">All Status</option>
                                <option value="completed">Completed</option>
                                <option value="pending">Pending</option>
                                <option value="processing">Processing</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Table Content -->
                <div class="relative">
                    <!-- Loading State -->
                    <div v-if="isLoading" class="loading-overlay">
                        <div class="text-center">
                            <div class="spinner mx-auto mb-4"></div>
                            <p class="text-gray-600">Loading orders...</p>
                        </div>
                    </div>

                    <!-- Error State -->
                    <div v-else-if="hasError" class="error-state">
                        <svg class="error-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        <h3 class="error-title">Failed to load orders</h3>
                        <p class="error-message">{{ errorMessage }}</p>
                        <button @click="retryLoad" class="retry-button">
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Try Again
                        </button>
                    </div>

                    <!-- Empty State -->
                    <div v-else-if="filteredRows.length === 0 && !isLoading" class="empty-state">
                        <svg class="empty-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="empty-title">
                            {{ searchQuery || statusFilter ? 'No matching orders found' : 'No orders yet' }}
                        </h3>
                        <p class="empty-message">
                            {{ searchQuery || statusFilter
                                ? 'Try adjusting your search criteria or filters to find what you\'re looking for.'
                                : 'Get started by creating your first order. Orders will appear here once they\'re created.'
                            }}
                        </p>
                        <button v-if="!searchQuery && !statusFilter" @click="createOrder" class="create-button">
                            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Create First Order
                        </button>
                        <button v-else @click="clearFilters" class="create-button">
                            <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                            Clear Filters
                        </button>
                    </div>

                    <!-- Skeleton Loading State -->
                    <div v-else-if="isInitialLoading">
                        <div class="skeleton-row" v-for="i in 5" :key="i">
                            <div class="skeleton-cell" style="width: 120px;"></div>
                            <div class="skeleton-cell" style="width: 280px;"></div>
                            <div class="skeleton-cell" style="width: 120px;"></div>
                            <div class="skeleton-cell" style="width: 120px;"></div>
                            <div class="skeleton-cell" style="width: 80px;"></div>
                            <div class="skeleton-cell" style="width: 140px;"></div>
                        </div>
                    </div>

                    <!-- Vue Good Table -->
                    <vue-good-table
                        v-else
                        :columns="columns"
                        :rows="filteredRows"
                        :pagination-options="paginationOptions"
                        :search-options="searchOptions"
                        :sort-options="sortOptions"
                        styleClass="vgt-table striped"
                    >
                        <template #table-row="props">
                            <span v-if="props.column.field === 'customer'">
                                <div class="customer-cell">
                                    <div class="customer-avatar">
                                        {{ getInitials(props.row.customer) }}
                                    </div>
                                    <div class="customer-info">
                                        <h4>{{ props.row.customer }}</h4>
                                        <p>{{ props.row.email }}</p>
                                    </div>
                                </div>
                            </span>
                            <span v-else-if="props.column.field === 'status'">
                                <span :class="['status-badge', `status-${props.row.status}`]">
                                    {{ props.row.status }}
                                </span>
                            </span>
                            <span v-else-if="props.column.field === 'amount'">
                                <span class="font-semibold text-gray-900">
                                    ${{ props.row.amount.toLocaleString('en-US', { minimumFractionDigits: 2 }) }}
                                </span>
                            </span>
                            <span v-else-if="props.column.field === 'created_at'">
                                {{ formatDate(props.row.created_at) }}
                            </span>
                            <span v-else-if="props.column.field === 'actions'">
                                <div class="action-buttons">
                                    <button
                                        @click="viewOrder(props.row)"
                                        class="action-button primary"
                                        title="View order details"
                                    >
                                        View
                                    </button>
                                    <button
                                        @click="editOrder(props.row)"
                                        class="action-button"
                                        title="Edit order"
                                        :disabled="props.row.status === 'cancelled'"
                                    >
                                        Edit
                                    </button>
                                    <button
                                        @click="deleteOrder(props.row)"
                                        class="action-button danger"
                                        title="Delete order"
                                        :disabled="props.row.status === 'completed'"
                                    >
                                        Delete
                                    </button>
                                </div>
                            </span>
                            <span v-else>
                                {{ props.formattedRow[props.column.field] }}
                            </span>
                        </template>
                    </vue-good-table>
                </div>
            </div>

            <!-- Statistics -->
            <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 bg-blue-500 rounded-md flex items-center justify-center">
                                    <span class="text-white font-semibold text-sm">{{ rows.length }}</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ rows.length }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 bg-green-500 rounded-md flex items-center justify-center">
                                    <span class="text-white font-semibold text-sm">{{ completedCount }}</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ completedCount }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                    <span class="text-white font-semibold text-sm">{{ pendingCount }}</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                                    <dd class="text-lg font-medium text-gray-900">{{ pendingCount }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
                                    <span class="text-white font-semibold text-sm">${{ totalRevenue.toLocaleString() }}</span>
                                </div>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Revenue</dt>
                                    <dd class="text-lg font-medium text-gray-900">${{ totalRevenue.toLocaleString() }}</dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div class="toast-container">
            <div
                v-for="toast in toasts"
                :key="toast.id"
                :class="['toast', toast.type]"
            >
                <div class="toast-header">
                    <span class="toast-title">{{ toast.title }}</span>
                    <button @click="removeToast(toast.id)" class="toast-close">
                        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="toast-message">{{ toast.message }}</div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { VueGoodTable } = window.VueGoodTableNext;

        createApp({
            components: {
                VueGoodTable
            },
            data() {
                return {
                    searchQuery: '',
                    statusFilter: '',
                    isLoading: false,
                    isInitialLoading: true,
                    isRefreshing: false,
                    hasError: false,
                    errorMessage: '',
                    toasts: [],
                    toastId: 0,
                    columns: [
                        {
                            label: 'Order ID',
                            field: 'order_id',
                            sortable: true,
                            width: '120px'
                        },
                        {
                            label: 'Customer',
                            field: 'customer',
                            sortable: true,
                            width: '280px'
                        },
                        {
                            label: 'Amount',
                            field: 'amount',
                            sortable: true,
                            type: 'number',
                            width: '120px'
                        },
                        {
                            label: 'Status',
                            field: 'status',
                            sortable: true,
                            width: '120px'
                        },
                        {
                            label: 'Items',
                            field: 'items',
                            sortable: true,
                            type: 'number',
                            width: '80px'
                        },
                        {
                            label: 'Date',
                            field: 'created_at',
                            sortable: true,
                            type: 'date',
                            width: '140px'
                        },
                        {
                            label: 'Actions',
                            field: 'actions',
                            sortable: false,
                            width: '180px'
                        }
                    ],
                    rows: [
                        {
                            id: 1,
                            order_id: 'ORD-001',
                            customer: 'John Doe',
                            email: '<EMAIL>',
                            amount: 1250.00,
                            status: 'completed',
                            created_at: '2024-01-15T10:30:00Z',
                            items: 3
                        },
                        {
                            id: 2,
                            order_id: 'ORD-002',
                            customer: 'Jane Smith',
                            email: '<EMAIL>',
                            amount: 875.50,
                            status: 'pending',
                            created_at: '2024-01-14T14:20:00Z',
                            items: 2
                        },
                        {
                            id: 3,
                            order_id: 'ORD-003',
                            customer: 'Bob Johnson',
                            email: '<EMAIL>',
                            amount: 2100.75,
                            status: 'processing',
                            created_at: '2024-01-13T09:15:00Z',
                            items: 5
                        },
                        {
                            id: 4,
                            order_id: 'ORD-004',
                            customer: 'Alice Brown',
                            email: '<EMAIL>',
                            amount: 450.25,
                            status: 'cancelled',
                            created_at: '2024-01-12T16:45:00Z',
                            items: 1
                        },
                        {
                            id: 5,
                            order_id: 'ORD-005',
                            customer: 'Charlie Wilson',
                            email: '<EMAIL>',
                            amount: 1875.00,
                            status: 'completed',
                            created_at: '2024-01-11T11:30:00Z',
                            items: 4
                        },
                        {
                            id: 6,
                            order_id: 'ORD-006',
                            customer: 'Diana Prince',
                            email: '<EMAIL>',
                            amount: 3200.00,
                            status: 'completed',
                            created_at: '2024-01-10T08:45:00Z',
                            items: 7
                        },
                        {
                            id: 7,
                            order_id: 'ORD-007',
                            customer: 'Clark Kent',
                            email: '<EMAIL>',
                            amount: 675.25,
                            status: 'processing',
                            created_at: '2024-01-09T15:20:00Z',
                            items: 2
                        },
                        {
                            id: 8,
                            order_id: 'ORD-008',
                            customer: 'Bruce Wayne',
                            email: '<EMAIL>',
                            amount: 5500.00,
                            status: 'completed',
                            created_at: '2024-01-08T12:10:00Z',
                            items: 12
                        }
                    ],
                    paginationOptions: {
                        enabled: true,
                        mode: 'records',
                        perPage: 5,
                        perPageDropdown: [5, 10, 15, 20],
                        dropdownAllowAll: false,
                        nextLabel: 'Next',
                        prevLabel: 'Previous',
                        rowsPerPageLabel: 'Rows per page',
                        ofLabel: 'of',
                        pageLabel: 'page'
                    },
                    searchOptions: {
                        enabled: false
                    },
                    sortOptions: {
                        enabled: true,
                        multipleColumns: false
                    }
                };
            },
            computed: {
                filteredRows() {
                    let filtered = this.rows;

                    // Apply status filter
                    if (this.statusFilter) {
                        filtered = filtered.filter(row => row.status === this.statusFilter);
                    }

                    // Apply search filter
                    if (this.searchQuery.trim()) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(row => {
                            return Object.values(row).some(value =>
                                String(value).toLowerCase().includes(query)
                            );
                        });
                    }

                    return filtered;
                },
                completedCount() {
                    return this.rows.filter(row => row.status === 'completed').length;
                },
                pendingCount() {
                    return this.rows.filter(row => row.status === 'pending').length;
                },
                totalRevenue() {
                    return this.rows
                        .filter(row => row.status === 'completed')
                        .reduce((sum, row) => sum + row.amount, 0);
                }
            },
            methods: {
                getInitials(name) {
                    return name
                        .split(' ')
                        .map(word => word.charAt(0))
                        .join('')
                        .toUpperCase();
                },
                formatDate(dateString) {
                    return new Date(dateString).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                },

                // Data loading methods
                async loadData() {
                    this.isLoading = true;
                    this.hasError = false;

                    try {
                        // Simulate API call
                        await this.simulateApiCall();
                        this.showToast('success', 'Success', 'Orders loaded successfully');
                    } catch (error) {
                        this.hasError = true;
                        this.errorMessage = error.message;
                        this.showToast('error', 'Error', 'Failed to load orders');
                    } finally {
                        this.isLoading = false;
                        this.isInitialLoading = false;
                    }
                },

                async refreshData() {
                    this.isRefreshing = true;
                    this.hasError = false;

                    try {
                        // Simulate API call
                        await this.simulateApiCall();
                        this.showToast('info', 'Refreshed', 'Data has been updated');
                    } catch (error) {
                        this.hasError = true;
                        this.errorMessage = error.message;
                        this.showToast('error', 'Error', 'Failed to refresh data');
                    } finally {
                        this.isRefreshing = false;
                    }
                },

                async retryLoad() {
                    await this.loadData();
                },

                simulateApiCall() {
                    return new Promise((resolve, reject) => {
                        setTimeout(() => {
                            // Simulate random success/failure (90% success rate)
                            if (Math.random() > 0.1) {
                                resolve();
                            } else {
                                reject(new Error('Network error: Unable to connect to server'));
                            }
                        }, 1500);
                    });
                },

                // Filter methods
                clearFilters() {
                    this.searchQuery = '';
                    this.statusFilter = '';
                    this.showToast('info', 'Filters Cleared', 'All filters have been reset');
                },

                // Action methods
                createOrder() {
                    this.showToast('info', 'Create Order', 'Redirecting to order creation form...');
                    // Simulate navigation
                    setTimeout(() => {
                        this.showToast('success', 'Order Created', 'New order has been created successfully');
                    }, 2000);
                },

                viewOrder(order) {
                    this.showToast('info', 'View Order', `Opening details for order ${order.order_id}`);
                },

                editOrder(order) {
                    if (order.status === 'cancelled') {
                        this.showToast('warning', 'Cannot Edit', 'Cancelled orders cannot be edited');
                        return;
                    }
                    this.showToast('info', 'Edit Order', `Opening editor for order ${order.order_id}`);
                },

                deleteOrder(order) {
                    if (order.status === 'completed') {
                        this.showToast('warning', 'Cannot Delete', 'Completed orders cannot be deleted');
                        return;
                    }

                    if (confirm(`Are you sure you want to delete order ${order.order_id}?`)) {
                        // Simulate deletion
                        const index = this.rows.findIndex(row => row.id === order.id);
                        if (index > -1) {
                            this.rows.splice(index, 1);
                            this.showToast('success', 'Order Deleted', `Order ${order.order_id} has been deleted`);
                        }
                    }
                },

                // Toast notification methods
                showToast(type, title, message) {
                    const toast = {
                        id: ++this.toastId,
                        type,
                        title,
                        message
                    };

                    this.toasts.push(toast);

                    // Auto remove after 5 seconds
                    setTimeout(() => {
                        this.removeToast(toast.id);
                    }, 5000);
                },

                removeToast(id) {
                    const index = this.toasts.findIndex(toast => toast.id === id);
                    if (index > -1) {
                        this.toasts.splice(index, 1);
                    }
                }
            },

            // Lifecycle hooks
            async mounted() {
                // Simulate initial loading
                await this.loadData();
            }
        }).mount('#app');
    </script>
</body>
</html>
