// UI Component exports for Vertigo AMS
// This file provides centralized exports for all UI components

// Atoms (Basic building blocks)
export { default as Button } from './Button.vue';
export { default as Input } from './Input.vue';
export { default as Card } from './Card.vue';
export { default as Modal } from './Modal.vue';
export { default as Table } from './Table.vue';
export { default as EnhancedTable } from './EnhancedTable.vue';
export { default as Navbar } from './Navbar.vue';
export { default as Breadcrumb } from './Breadcrumb.vue';
export { default as Dropdown } from './Dropdown.vue';
export { default as Select } from './Select.vue';
export { default as Badge } from './Badge.vue';
export { default as Pagination } from './Pagination.vue';
export { default as Notification } from './Notification.vue';
export { default as NotificationContainer } from './NotificationContainer.vue';
export { default as Tooltip } from './Tooltip.vue';
export { default as Alert } from './Alert.vue';
export { default as Toast } from './Toast.vue';
export { default as Loading } from './Loading.vue';
export { default as Progress } from './Progress.vue';
export { default as Tabs } from './Tabs.vue';
export { default as Banner } from './Banner.vue';
export { default as Hero } from './Hero.vue';
export { default as HorizontalSlider } from './HorizontalSlider.vue';

// Auction-specific components
export * from '../auction';

// Authentication components
export * from '../auth';

// Dashboard and analytics components
export * from '../dashboard';

// Form Components
export { default as Form } from '../forms/Form.vue';
export { default as FormField } from '../forms/FormField.vue';
export { default as FormGroup } from '../forms/FormGroup.vue';
export { default as FormInput } from '../forms/FormInput.vue';
export { default as FormTextarea } from '../forms/FormTextarea.vue';
export { default as FormFileUpload } from '../forms/FormFileUpload.vue';

// Layout Components
export { default as AppLayout } from '../layout/AppLayout.vue';
export { default as Header } from '../layout/Header.vue';
export { default as Sidebar } from '../layout/Sidebar.vue';
export { default as Footer } from '../layout/Footer.vue';
export { default as Container } from '../layout/Container.vue';
export { default as Grid } from '../layout/Grid.vue';
export { default as GridItem } from '../layout/GridItem.vue';

// Component type exports
export type {
  ButtonProps,
  InputProps,
  TableProps,
  TableColumn,
  TableSortEvent,
  TableSelectEvent,
  NavItem,
  BreadcrumbItem,
  DropdownItem,
  SelectItem,
  BadgeProps,
  PaginationProps,
  FormInputProps,
  FormTextareaProps,
  FormFileUploadProps,
  FileItem
} from '@/types';
