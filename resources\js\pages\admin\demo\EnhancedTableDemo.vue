<template>
  <div class="space-y-8">
    <!-- <PERSON> Header -->
    <div>
      <h1 class="text-3xl font-bold text-gray-900">Enhanced Table Demo</h1>
      <p class="mt-2 text-gray-600">
        Showcase of the enhanced table component with advanced features like filtering, bulk actions, and responsive design.
      </p>
    </div>

    <!-- Basic Enhanced Table -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold text-gray-900">Basic Enhanced Table</h2>
      <EnhancedTable
        :data="sampleData"
        :columns="basicColumns"
        :loading="loading"
        title="Sample Orders"
        subtitle="Manage your orders with advanced table features"
        searchable
        filterable
        :filters="sampleFilters"
        exportable
        selectable
        create-button
        create-button-text="Create Order"
        :row-actions="rowActions"
        :bulk-actions="bulkActions"
        striped
        hover
        @create="handleCreate"
        @row-action="handleRowAction"
        @bulk-action="handleBulkAction"
        @row-click="handleRowClick"
        @export="handleExport"
        @filter-change="handleFilterChange"
      >
        <!-- Custom column templates -->
        <template #column-customer="{ row, value }">
          <div class="flex items-center">
            <div class="flex-shrink-0 h-10 w-10">
              <div class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                <span class="text-sm font-medium text-white">
                  {{ getInitials(row.customer) }}
                </span>
              </div>
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-900">{{ row.customer }}</div>
              <div class="text-sm text-gray-500">{{ row.email }}</div>
            </div>
          </div>
        </template>

        <template #column-status="{ row, value }">
          <span :class="getStatusClasses(value)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
            {{ value }}
          </span>
        </template>

        <template #column-amount="{ row, value }">
          <span class="text-sm font-medium text-gray-900">
            ${{ value.toLocaleString('en-US', { minimumFractionDigits: 2 }) }}
          </span>
        </template>

        <template #column-created_at="{ row, value }">
          <div class="text-sm text-gray-900">
            {{ formatDate(value) }}
          </div>
        </template>
      </EnhancedTable>
    </div>

    <!-- Compact Table -->
    <div class="space-y-4">
      <h2 class="text-xl font-semibold text-gray-900">Compact Table</h2>
      <EnhancedTable
        :data="compactData"
        :columns="compactColumns"
        title="Recent Activities"
        subtitle="Latest system activities"
        :show-header="true"
        :searchable="false"
        :filterable="false"
        :exportable="false"
        :selectable="false"
        :create-button="false"
        :striped="false"
        :bordered="true"
        :per-page="5"
        empty-state-title="No activities yet"
        empty-state-message="Activities will appear here as they occur."
      >
        <template #column-type="{ row, value }">
          <div class="flex items-center">
            <div :class="getActivityIconClasses(value)" class="flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center">
              <component :is="getActivityIcon(value)" class="h-4 w-4" />
            </div>
            <span class="ml-3 text-sm font-medium text-gray-900">{{ value }}</span>
          </div>
        </template>

        <template #column-timestamp="{ row, value }">
          <span class="text-sm text-gray-500">{{ formatRelativeTime(value) }}</span>
        </template>
      </EnhancedTable>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-blue-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold text-sm">{{ sampleData.length }}</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                <dd class="text-lg font-medium text-gray-900">{{ sampleData.length }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-green-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold text-sm">{{ completedOrders }}</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                <dd class="text-lg font-medium text-gray-900">{{ completedOrders }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-yellow-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold text-sm">{{ pendingOrders }}</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                <dd class="text-lg font-medium text-gray-900">{{ pendingOrders }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
                <span class="text-white font-semibold text-sm">${{ totalRevenue.toLocaleString() }}</span>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                <dd class="text-lg font-medium text-gray-900">${{ totalRevenue.toLocaleString() }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import EnhancedTable from '@/components/ui/EnhancedTable.vue';
import {
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentDuplicateIcon,
  ArchiveBoxIcon,
  UserIcon,
  ShoppingCartIcon,
  CogIcon,
  ExclamationTriangleIcon,
} from '@heroicons/vue/24/outline';

// Reactive state
const loading = ref(false);

// Sample data
const sampleData = ref([
  {
    id: 1,
    order_id: 'ORD-001',
    customer: 'John Doe',
    email: '<EMAIL>',
    amount: 1250.00,
    status: 'completed',
    created_at: '2024-01-15T10:30:00Z',
    items: 3,
  },
  {
    id: 2,
    order_id: 'ORD-002',
    customer: 'Jane Smith',
    email: '<EMAIL>',
    amount: 875.50,
    status: 'pending',
    created_at: '2024-01-14T14:20:00Z',
    items: 2,
  },
  {
    id: 3,
    order_id: 'ORD-003',
    customer: 'Bob Johnson',
    email: '<EMAIL>',
    amount: 2100.75,
    status: 'processing',
    created_at: '2024-01-13T09:15:00Z',
    items: 5,
  },
  {
    id: 4,
    order_id: 'ORD-004',
    customer: 'Alice Brown',
    email: '<EMAIL>',
    amount: 450.25,
    status: 'cancelled',
    created_at: '2024-01-12T16:45:00Z',
    items: 1,
  },
  {
    id: 5,
    order_id: 'ORD-005',
    customer: 'Charlie Wilson',
    email: '<EMAIL>',
    amount: 1875.00,
    status: 'completed',
    created_at: '2024-01-11T11:30:00Z',
    items: 4,
  },
]);

const compactData = ref([
  {
    id: 1,
    type: 'User Login',
    description: 'John Doe logged into the system',
    timestamp: '2024-01-15T10:30:00Z',
  },
  {
    id: 2,
    type: 'Order Created',
    description: 'New order ORD-006 was created',
    timestamp: '2024-01-15T10:25:00Z',
  },
  {
    id: 3,
    type: 'System Update',
    description: 'System maintenance completed',
    timestamp: '2024-01-15T09:00:00Z',
  },
  {
    id: 4,
    type: 'Error',
    description: 'Payment processing error occurred',
    timestamp: '2024-01-15T08:45:00Z',
  },
]);

// Table columns
const basicColumns = [
  {
    label: 'Order ID',
    field: 'order_id',
    sortable: true,
    width: '120px',
  },
  {
    label: 'Customer',
    field: 'customer',
    sortable: true,
    width: '250px',
  },
  {
    label: 'Amount',
    field: 'amount',
    sortable: true,
    type: 'number',
    width: '120px',
  },
  {
    label: 'Status',
    field: 'status',
    sortable: true,
    width: '120px',
  },
  {
    label: 'Items',
    field: 'items',
    sortable: true,
    type: 'number',
    width: '80px',
  },
  {
    label: 'Date',
    field: 'created_at',
    sortable: true,
    type: 'date',
    width: '140px',
  },
];

const compactColumns = [
  {
    label: 'Type',
    field: 'type',
    sortable: true,
    width: '200px',
  },
  {
    label: 'Description',
    field: 'description',
    sortable: false,
  },
  {
    label: 'Time',
    field: 'timestamp',
    sortable: true,
    width: '120px',
  },
];

// Filters
const sampleFilters = [
  {
    key: 'status',
    label: 'Status',
    type: 'select' as const,
    placeholder: 'All statuses',
    options: [
      { label: 'Completed', value: 'completed' },
      { label: 'Pending', value: 'pending' },
      { label: 'Processing', value: 'processing' },
      { label: 'Cancelled', value: 'cancelled' },
    ],
  },
  {
    key: 'amount',
    label: 'Amount Range',
    type: 'text' as const,
    placeholder: 'Min amount',
  },
  {
    key: 'created_at',
    label: 'Date Range',
    type: 'daterange' as const,
  },
];

// Actions
const rowActions = [
  {
    key: 'view',
    label: 'View',
    icon: EyeIcon,
    variant: 'ghost' as const,
  },
  {
    key: 'edit',
    label: 'Edit',
    icon: PencilIcon,
    variant: 'ghost' as const,
  },
  {
    key: 'delete',
    label: 'Delete',
    icon: TrashIcon,
    variant: 'ghost' as const,
  },
];

const bulkActions = [
  {
    key: 'duplicate',
    label: 'Duplicate',
    icon: DocumentDuplicateIcon,
    variant: 'outline' as const,
  },
  {
    key: 'archive',
    label: 'Archive',
    icon: ArchiveBoxIcon,
    variant: 'outline' as const,
  },
  {
    key: 'delete',
    label: 'Delete',
    icon: TrashIcon,
    variant: 'danger' as const,
  },
];

// Computed properties
const completedOrders = computed(() =>
  sampleData.value.filter(order => order.status === 'completed').length
);

const pendingOrders = computed(() =>
  sampleData.value.filter(order => order.status === 'pending').length
);

const totalRevenue = computed(() =>
  sampleData.value
    .filter(order => order.status === 'completed')
    .reduce((sum, order) => sum + order.amount, 0)
);

// Methods
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase();
};

const getStatusClasses = (status: string): string => {
  const classes = {
    completed: 'bg-green-100 text-green-800',
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    cancelled: 'bg-red-100 text-red-800',
  };
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800';
};

const getActivityIcon = (type: string) => {
  const icons = {
    'User Login': UserIcon,
    'Order Created': ShoppingCartIcon,
    'System Update': CogIcon,
    'Error': ExclamationTriangleIcon,
  };
  return icons[type as keyof typeof icons] || UserIcon;
};

const getActivityIconClasses = (type: string): string => {
  const classes = {
    'User Login': 'bg-blue-100 text-blue-600',
    'Order Created': 'bg-green-100 text-green-600',
    'System Update': 'bg-purple-100 text-purple-600',
    'Error': 'bg-red-100 text-red-600',
  };
  return classes[type as keyof typeof classes] || 'bg-gray-100 text-gray-600';
};

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const formatRelativeTime = (dateString: string): string => {
  const now = new Date();
  const date = new Date(dateString);
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  return `${diffInDays}d ago`;
};

// Event handlers
const handleCreate = () => {
  console.log('Create new order');
  // Add your create logic here
};

const handleRowAction = (action: string, row: any, index: number) => {
  console.log('Row action:', action, row, index);
  // Add your row action logic here
};

const handleBulkAction = (action: string, rows: any[]) => {
  console.log('Bulk action:', action, rows);
  // Add your bulk action logic here
};

const handleRowClick = (row: any, index: number) => {
  console.log('Row clicked:', row, index);
  // Add your row click logic here
};

const handleExport = (data: any[]) => {
  console.log('Export data:', data);
  // Add your export logic here

  // Example: Export as CSV
  const csv = [
    // Headers
    basicColumns.map(col => col.label).join(','),
    // Data rows
    ...data.map(row =>
      basicColumns.map(col => {
        const value = row[col.field];
        return typeof value === 'string' && value.includes(',')
          ? `"${value}"`
          : value;
      }).join(',')
    )
  ].join('\n');

  const blob = new Blob([csv], { type: 'text/csv' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'orders.csv';
  a.click();
  window.URL.revokeObjectURL(url);
};

const handleFilterChange = (filters: Record<string, any>) => {
  console.log('Filters changed:', filters);
  // Add your filter logic here
};
</script>
