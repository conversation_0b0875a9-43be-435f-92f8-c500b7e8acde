<template>
  <div :class="sidebarClasses">
    <!-- Sidebar Header -->
    <div class="p-4 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <!-- <PERSON><PERSON> and Brand -->
        <div v-if="!adminStore.sidebarCollapsed" class="flex items-center">
          <div class="w-12 h-12 mr-4">
            <svg viewBox="0 0 260 260" class="w-full h-full drop-shadow-sm">
              <circle fill="#243b53" cx="130" cy="130" r="130"/>
              <path fill="#fff" d="M184.38,167.7h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
              <path fill="#fff" d="M125.73,128.23v15.55h-13.22l2.79,3.7-2.79,4.66h13.22v15.56h-40.82v-15.54l.74-1.24,2.21-3.7-2.95-3.87v-10.93l2.51-4.2h38.31Z"/>
              <path fill="#fff" d="M184.38,123.37h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
              <path fill="#fff" d="M96.65,123.37v-20.91h-12.65v-18.55h44.35v18.55h-12.65v20.91h-19.04Z"/>
              <polygon fill="#fff" points="227.59 173.63 224 172.2 223.61 169.86 221.71 171.29 218.13 169.86 224.52 164.98 226.31 165.69 227.59 173.63"/>
              <polygon fill="#fff" points="31.03 173.63 34.62 172.2 35 169.86 36.9 171.29 40.48 169.86 34.1 164.98 32.3 165.69 31.03 173.63"/>
            </svg>
          </div>
          <div class="hidden sm:block">
            <h1 class="text-xl font-bold text-gray-900">Vertigo AMS</h1>
            <p class="text-sm text-gray-500">Admin Panel</p>
          </div>
        </div>
        
        <!-- Collapsed Logo -->
        <div v-else class="w-12 h-12 mx-auto">
          <svg viewBox="0 0 260 260" class="w-full h-full drop-shadow-sm">
            <circle fill="#243b53" cx="130" cy="130" r="130"/>
            <path fill="#fff" d="M184.38,167.7h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
            <path fill="#fff" d="M125.73,128.23v15.55h-13.22l2.79,3.7-2.79,4.66h13.22v15.56h-40.82v-15.54l.74-1.24,2.21-3.7-2.95-3.87v-10.93l2.51-4.2h38.31Z"/>
            <path fill="#fff" d="M184.38,123.37h-22.08l-7.01-11.62-7.05,11.62h-22.03l23.56-39.47h11.04l23.56,39.47Z"/>
            <path fill="#fff" d="M96.65,123.37v-20.91h-12.65v-18.55h44.35v18.55h-12.65v20.91h-19.04Z"/>
            <polygon fill="#fff" points="227.59 173.63 224 172.2 223.61 169.86 221.71 171.29 218.13 169.86 224.52 164.98 226.31 165.69 227.59 173.63"/>
            <polygon fill="#fff" points="31.03 173.63 34.62 172.2 35 169.86 36.9 171.29 40.48 169.86 34.1 164.98 32.3 165.69 31.03 173.63"/>
          </svg>
        </div>

        <!-- Collapse Toggle Button -->
        <button
          @click="adminStore.toggleSidebar"
          class="hidden md:block p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path 
              stroke-linecap="round" 
              stroke-linejoin="round" 
              stroke-width="2" 
              :d="adminStore.sidebarCollapsed ? 'M9 5l7 7-7 7' : 'M15 19l-7-7 7-7'"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Navigation Menu -->
    <AdminNavigation />

    <!-- User Info -->
    <div class="p-4 border-t border-gray-200">
      <div v-if="!adminStore.sidebarCollapsed" class="flex items-center">
        <div class="h-10 w-10 bg-primary-500 rounded-full flex items-center justify-center mr-3">
          <span class="text-white font-medium">{{ userInitials }}</span>
        </div>
        <div class="flex-1">
          <p class="text-sm font-medium text-gray-900">{{ userName }}</p>
          <p class="text-xs text-gray-500">{{ userRole }}</p>
        </div>
        <button 
          @click="handleLogout"
          class="p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200"
          title="Logout"
        >
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
          </svg>
        </button>
      </div>
      <div v-else class="flex justify-center">
        <div class="h-10 w-10 bg-primary-500 rounded-full flex items-center justify-center">
          <span class="text-white font-medium">{{ userInitials }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useAdminStore } from '@/stores/admin';
import { useAuthStore } from '@/stores/auth';
import AdminNavigation from '../navigation/AdminNavigation.vue';

// Stores
const adminStore = useAdminStore();
const authStore = useAuthStore();

// Computed properties
const sidebarClasses = computed(() => [
  'admin-sidebar bg-white border-r border-gray-200 flex flex-col z-50 transition-all duration-300',
  adminStore.sidebarCollapsed ? 'w-16' : 'w-72',
  'md:relative md:translate-x-0',
  adminStore.mobileMenuOpen ? 'mobile-open' : '',
  // Mobile positioning
  'md:static fixed top-0 left-0 h-full',
  adminStore.mobileMenuOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
]);

const userName = computed(() => {
  return authStore.user?.name || 'Admin User';
});

const userRole = computed(() => {
  return authStore.user?.roles?.[0]?.name || 'Administrator';
});

const userInitials = computed(() => {
  const name = userName.value;
  return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
});

// Methods
const handleLogout = async () => {
  try {
    await authStore.logout();
    // Redirect to login or home page
    window.location.href = '/login';
  } catch (error) {
    console.error('Logout failed:', error);
  }
};
</script>

<style scoped>
.admin-sidebar {
  transition: width 0.3s ease;
}

.admin-sidebar.collapsed {
  width: 4rem;
}

/* Mobile styles */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 280px !important;
    max-width: 85vw;
  }
}

@media (max-width: 640px) {
  .admin-sidebar {
    width: 100vw !important;
    max-width: 100vw !important;
  }
}

/* Safe area support for devices with notches */
.safe-area-padding {
  padding-left: max(1rem, env(safe-area-inset-left));
  padding-right: max(1rem, env(safe-area-inset-right));
}
</style>
