@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }
}

/* Custom component styles */
@layer components {
  /* Modern Button Styles */
  .btn {
    @apply inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-500 text-white shadow-sm hover:bg-primary-600 focus:ring-primary-500 active:bg-primary-700;
  }

  .btn-secondary {
    @apply bg-brand-800 text-white shadow-sm hover:bg-brand-900 focus:ring-brand-700 active:bg-brand-900;
  }

  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100;
  }

  .btn-ghost {
    @apply border-transparent text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200;
  }

  .btn-danger {
    @apply bg-red-600 text-white shadow-sm hover:bg-red-700 focus:ring-red-500 active:bg-red-800;
  }

  .btn-success {
    @apply bg-green-600 text-white shadow-sm hover:bg-green-700 focus:ring-green-500 active:bg-green-800;
  }

  .card {
    @apply bg-white overflow-hidden shadow rounded-lg;
  }

  /* Admin-specific component styles */
  .admin-sidebar {
    transition: width 0.3s ease;
  }

  .admin-sidebar.collapsed {
    width: 4rem;
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
  }

  .nav-shadow {
    box-shadow: 0 4px 20px rgba(0, 104, 255, 0.1);
  }

  .menu-item {
    transition: all 0.2s ease;
  }

  .menu-item:hover {
    transform: translateX(4px);
  }

  /* Mobile touch targets */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
    -webkit-tap-highlight-color: rgba(0, 104, 255, 0.1);
  }

  /* Enhanced Input Styles - Ring only, no borders */
  .form-input {
    @apply block w-full rounded-lg border-0 py-2 px-3 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-all duration-200 sm:text-sm sm:leading-6;
  }

  .form-input:hover:not(:disabled) {
    @apply ring-gray-400;
  }

  .form-input:disabled {
    @apply bg-gray-50 text-gray-500 ring-gray-200 cursor-not-allowed;
  }

  .form-input.error {
    @apply ring-red-300 text-red-900 placeholder:text-red-300 focus:ring-red-500;
  }

  .form-input.success {
    @apply ring-green-300 text-green-900 placeholder:text-green-300 focus:ring-green-500;
  }

  /* Override any conflicting styles from Tailwind Forms plugin */
  /* input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="search"],
  input[type="tel"],
  input[type="url"] {
    border: 0 !important;
  } */

  /* Ensure focus states use only rings - no borders or box-shadows */
  input:focus {
    border-color: transparent !important;
    box-shadow: none !important;
    outline: none !important;
  }

  /* Fix error state focus - single red ring only */
  input.error:focus,
  .form-input.error:focus,
  input[class*="ring-red"]:focus {
    --tw-ring-color: rgb(239 68 68) !important; /* red-500 */
    --tw-ring-opacity: 1 !important;
    border-color: transparent !important;
    box-shadow: 0 0 0 2px rgb(239 68 68) !important;
  }

  /* Fix success state focus - single green ring only */
  input.success:focus,
  .form-input.success:focus,
  input[class*="ring-green"]:focus {
    --tw-ring-color: rgb(34 197 94) !important; /* green-500 */
    --tw-ring-opacity: 1 !important;
    border-color: transparent !important;
    box-shadow: 0 0 0 2px rgb(34 197 94) !important;
  }

  /* Ensure normal focus state works properly */
  input:not(.error):not(.success):focus {
    --tw-ring-color: #0068ff !important; /* brand primary */
    --tw-ring-opacity: 1 !important;
    border-color: transparent !important;
    box-shadow: 0 0 0 2px #0068ff !important;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-label.error {
    @apply text-red-700;
  }

  .form-label.disabled {
    @apply text-gray-400;
  }

  /* Light Clean Vue Good Table Styles */
  .vgt-table {
    @apply w-full bg-white;
    color: #374151 !important;
    background-color: white !important;
    border: none;
    border-radius: 0;
  }

  /* Force light mode for all table elements */
  .vgt-table,
  .vgt-table * {
    background-color: white !important;
    color: #374151 !important;
  }

  /* Clean Header Styling - No vertical borders */
  .vgt-table thead th {
    text-align: left;
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280 !important;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.75rem 1.5rem;
    border-bottom: 1px solid #f3f4f6 !important;
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;
    background-color: white !important;
    transition: all 0.2s ease;
  }

  .vgt-table thead th:first-child {
    border-top-left-radius: 0;
  }

  .vgt-table thead th:last-child {
    border-top-right-radius: 0;
  }

  /* Clean sortable header styling */
  .vgt-table thead th.sortable {
    cursor: pointer;
    user-select: none;
  }

  .vgt-table thead th.sortable:hover {
    background-color: #f9fafb !important;
    color: #374151 !important;
  }

  /* Clean Body Styling - No vertical borders */
  .vgt-table tbody td {
    padding: 1rem 1.5rem;
    font-size: 0.875rem;
    color: #374151 !important;
    border-bottom: 1px solid #f9fafb !important;
    border-left: none !important;
    border-right: none !important;
    border-top: none !important;
    background-color: white !important;
    transition: all 0.15s ease;
  }

  .vgt-table tbody tr {
    background-color: white !important;
    transition: all 0.15s ease;
  }

  /* Subtle hover effects */
  .vgt-table tbody tr:hover {
    background-color: #f8fafc !important;
  }

  .vgt-table tbody tr:hover td {
    background-color: #f8fafc !important;
    color: #374151 !important;
  }

  /* Clean alternating row colors */
  .vgt-table.striped tbody tr:nth-child(even) {
    background-color: #fafbfc !important;
  }

  .vgt-table.striped tbody tr:nth-child(even):hover {
    background-color: #f8fafc !important;
  }

  .vgt-table.striped tbody tr:nth-child(even):hover td {
    background-color: #f8fafc !important;
  }

  /* Clean Loading State */
  .vgt-table .vgt-loading {
    text-align: center;
    padding: 2rem 0;
    color: #9ca3af !important;
    background-color: white !important;
    font-size: 0.875rem;
  }

  .vgt-table .vgt-loading::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f4f6;
    border-top: 2px solid #6b7280;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Clean Empty State */
  .vgt-table .vgt-empty-state {
    text-align: center;
    padding: 3rem 0;
    color: #9ca3af !important;
    background-color: white !important;
    font-size: 0.875rem;
  }

  /* Clean Pagination */
  .vgt-table .vgt-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background-color: white !important;
    border-top: 1px solid #f3f4f6 !important;
  }

  .vgt-table .vgt-pagination .vgt-pagination-info {
    font-size: 0.875rem;
    font-weight: 400;
    color: #6b7280 !important;
  }

  .vgt-table .vgt-pagination .vgt-pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  /* Clean Pagination Buttons */
  .vgt-table .vgt-pagination button {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 400;
    border: 1px solid #e5e7eb !important;
    border-radius: 0.375rem;
    background-color: white !important;
    color: #6b7280 !important;
    transition: all 0.15s ease;
    min-width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .vgt-table .vgt-pagination button:hover:not(:disabled) {
    background-color: #f9fafb !important;
    border-color: #d1d5db !important;
    color: #374151 !important;
  }

  .vgt-table .vgt-pagination button.active {
    background-color: #f3f4f6 !important;
    color: #374151 !important;
    border-color: #d1d5db !important;
    font-weight: 500;
  }

  .vgt-table .vgt-pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Clean Search Input */
  .vgt-table .vgt-search {
    margin-bottom: 1rem;
  }

  .vgt-table .vgt-search input {
    display: block;
    width: 100%;
    border-radius: 0.375rem;
    border: 1px solid #e5e7eb;
    padding: 0.5rem 0.75rem;
    color: #374151 !important;
    background-color: white !important;
    transition: all 0.2s ease;
    font-size: 0.875rem;
  }

  .vgt-table .vgt-search input:focus {
    outline: none;
    border-color: #d1d5db;
    box-shadow: 0 0 0 1px #d1d5db !important;
  }

  /* Override any dark theme styles */
  .vgt-table.theme-dark,
  .vgt-table.nocturnal,
  .vgt-table[class*="dark"] {
    background-color: white !important;
    color: #374151 !important;
  }

  .vgt-table.theme-dark *,
  .vgt-table.nocturnal *,
  .vgt-table[class*="dark"] * {
    background-color: inherit !important;
    color: inherit !important;
  }

  /* Clean Sort Icons */
  .vgt-table th .sort-icon {
    color: #9ca3af !important;
    transition: all 0.2s ease;
  }

  .vgt-table th.sortable:hover .sort-icon {
    color: #6b7280 !important;
  }

  /* Remove any box shadows or borders from table wrapper */
  .vgt-table-wrapper,
  .vgt-wrap {
    border: none !important;
    box-shadow: none !important;
  }

  /* Responsive enhancements */
  @media (max-width: 768px) {
    .vgt-table thead th,
    .vgt-table tbody td {
      padding: 0.75rem 1rem;
    }

    .vgt-table .vgt-pagination {
      padding: 0.75rem 1rem;
      flex-direction: column;
      gap: 0.75rem;
    }

    .vgt-table .vgt-pagination .vgt-pagination-controls {
      justify-content: center;
    }
  }
}

/* Custom utility styles */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Admin-specific utilities */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .safe-area-padding {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }

  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  /* Admin mobile optimizations */
  @media (max-width: 768px) {
    .admin-sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      z-index: 50;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      width: 280px !important;
      max-width: 85vw;
    }

    .admin-sidebar.mobile-open {
      transform: translateX(0);
    }

    .admin-content {
      margin-left: 0 !important;
    }

    /* Prevent body scroll when mobile menu is open */
    body.mobile-menu-open {
      overflow: hidden;
      position: fixed;
      width: 100%;
    }

    /* Disable hover effects on mobile */
    .menu-item:hover {
      transform: none;
    }
  }

  /* Prevent horizontal scroll on small screens */
  @media (max-width: 640px) {
    .admin-sidebar {
      width: 100vw !important;
      max-width: 100vw !important;
    }
  }
}

