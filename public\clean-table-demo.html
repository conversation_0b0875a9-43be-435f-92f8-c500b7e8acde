<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clean Table Demo - Vertigo AMS</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/vue-good-table-next@0.1.7/dist/vue-good-table-next.umd.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/vue-good-table-next@0.1.7/dist/vue-good-table-next.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Light Clean Vue Good Table Styles - No Vertical Borders */
        .vgt-table {
            width: 100%;
            background-color: white;
            color: #374151 !important;
            border: none;
            border-radius: 0;
        }

        .vgt-table,
        .vgt-table * {
            background-color: white !important;
            color: #374151 !important;
        }

        /* Clean Header - No vertical borders */
        .vgt-table thead th {
            text-align: left;
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280 !important;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: 0.75rem 1.5rem;
            border-bottom: 1px solid #f3f4f6 !important;
            border-left: none !important;
            border-right: none !important;
            border-top: none !important;
            background-color: white !important;
            transition: all 0.2s ease;
        }

        .vgt-table thead th.sortable {
            cursor: pointer;
            user-select: none;
        }

        .vgt-table thead th.sortable:hover {
            background-color: #f9fafb !important;
            color: #374151 !important;
        }

        /* Clean Body - No vertical borders */
        .vgt-table tbody td {
            padding: 1rem 1.5rem;
            font-size: 0.875rem;
            color: #374151 !important;
            border-bottom: 1px solid #f9fafb !important;
            border-left: none !important;
            border-right: none !important;
            border-top: none !important;
            background-color: white !important;
            transition: all 0.15s ease;
        }

        .vgt-table tbody tr {
            background-color: white !important;
            transition: all 0.15s ease;
        }

        .vgt-table tbody tr:hover {
            background-color: #f8fafc !important;
        }

        .vgt-table tbody tr:hover td {
            background-color: #f8fafc !important;
            color: #374151 !important;
        }

        /* Clean alternating rows */
        .vgt-table.striped tbody tr:nth-child(even) {
            background-color: #fafbfc !important;
        }

        .vgt-table.striped tbody tr:nth-child(even):hover {
            background-color: #f8fafc !important;
        }

        .vgt-table.striped tbody tr:nth-child(even):hover td {
            background-color: #f8fafc !important;
        }

        /* Clean Pagination */
        .vgt-table .vgt-pagination {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            background-color: white !important;
            border-top: 1px solid #f3f4f6 !important;
        }

        .vgt-table .vgt-pagination .vgt-pagination-info {
            font-size: 0.875rem;
            font-weight: 400;
            color: #6b7280 !important;
        }

        .vgt-table .vgt-pagination .vgt-pagination-controls {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .vgt-table .vgt-pagination button {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            font-weight: 400;
            border: 1px solid #e5e7eb !important;
            border-radius: 0.375rem;
            background-color: white !important;
            color: #6b7280 !important;
            transition: all 0.15s ease;
            min-width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .vgt-table .vgt-pagination button:hover:not(:disabled) {
            background-color: #f9fafb !important;
            border-color: #d1d5db !important;
            color: #374151 !important;
        }

        .vgt-table .vgt-pagination button.active {
            background-color: #f3f4f6 !important;
            color: #374151 !important;
            border-color: #d1d5db !important;
            font-weight: 500;
        }

        .vgt-table .vgt-pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Clean Search */
        .vgt-table .vgt-search {
            margin-bottom: 1rem;
        }

        .vgt-table .vgt-search input {
            display: block;
            width: 100%;
            border-radius: 0.375rem;
            border: 1px solid #e5e7eb;
            padding: 0.5rem 0.75rem;
            color: #374151 !important;
            background-color: white !important;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .vgt-table .vgt-search input:focus {
            outline: none;
            border-color: #d1d5db;
            box-shadow: 0 0 0 1px #d1d5db !important;
        }

        /* Clean Sort Icons */
        .vgt-table th .sort-icon {
            color: #9ca3af !important;
            transition: all 0.2s ease;
        }

        .vgt-table th.sortable:hover .sort-icon {
            color: #6b7280 !important;
        }

        /* Remove any wrapper borders */
        .vgt-table-wrapper,
        .vgt-wrap {
            border: none !important;
            box-shadow: none !important;
        }

        /* Custom status badges */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: capitalize;
        }

        .status-completed { background-color: #f0fdf4; color: #166534; }
        .status-pending { background-color: #fffbeb; color: #92400e; }
        .status-processing { background-color: #eff6ff; color: #1e40af; }
        .status-cancelled { background-color: #fef2f2; color: #dc2626; }

        /* Custom customer cell */
        .customer-cell {
            display: flex;
            align-items: center;
        }

        .customer-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-weight: 500;
            font-size: 0.75rem;
            margin-right: 0.75rem;
        }

        .customer-info h4 {
            font-weight: 500;
            color: #374151;
            margin: 0;
            font-size: 0.875rem;
        }

        .customer-info p {
            font-size: 0.75rem;
            color: #9ca3af;
            margin: 0;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen py-8">
    <div id="app">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-2xl font-semibold text-gray-900 mb-2">Clean Table Design</h1>
                <p class="text-gray-600">Light, clean table design without vertical borders for better readability</p>
            </div>

            <!-- Clean Table -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <!-- Table Header -->
                <div class="px-6 py-4 border-b border-gray-100">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Orders</h3>
                            <p class="text-sm text-gray-500 mt-1">{{ filteredRows.length }} total orders</p>
                        </div>
                        <div class="flex gap-3">
                            <button class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                Export
                            </button>
                            <button class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                Add Order
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Vue Good Table -->
                <vue-good-table
                    :columns="columns"
                    :rows="filteredRows"
                    :pagination-options="paginationOptions"
                    :search-options="searchOptions"
                    :sort-options="sortOptions"
                    styleClass="vgt-table striped"
                >
                    <template #table-row="props">
                        <span v-if="props.column.field === 'customer'">
                            <div class="customer-cell">
                                <div class="customer-avatar">
                                    {{ getInitials(props.row.customer) }}
                                </div>
                                <div class="customer-info">
                                    <h4>{{ props.row.customer }}</h4>
                                    <p>{{ props.row.email }}</p>
                                </div>
                            </div>
                        </span>
                        <span v-else-if="props.column.field === 'status'">
                            <span :class="['status-badge', `status-${props.row.status}`]">
                                {{ props.row.status }}
                            </span>
                        </span>
                        <span v-else-if="props.column.field === 'amount'">
                            <span class="font-medium text-gray-900">
                                ${{ props.row.amount.toLocaleString('en-US', { minimumFractionDigits: 2 }) }}
                            </span>
                        </span>
                        <span v-else-if="props.column.field === 'created_at'">
                            <span class="text-gray-600">
                                {{ formatDate(props.row.created_at) }}
                            </span>
                        </span>
                        <span v-else>
                            {{ props.formattedRow[props.column.field] }}
                        </span>
                    </template>
                </vue-good-table>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { VueGoodTable } = window.VueGoodTableNext;

        createApp({
            components: {
                VueGoodTable
            },
            data() {
                return {
                    searchQuery: '',
                    columns: [
                        {
                            label: 'Order ID',
                            field: 'order_id',
                            sortable: true,
                            width: '100px'
                        },
                        {
                            label: 'Customer',
                            field: 'customer',
                            sortable: true,
                            width: '280px'
                        },
                        {
                            label: 'Amount',
                            field: 'amount',
                            sortable: true,
                            type: 'number',
                            width: '120px'
                        },
                        {
                            label: 'Status',
                            field: 'status',
                            sortable: true,
                            width: '120px'
                        },
                        {
                            label: 'Items',
                            field: 'items',
                            sortable: true,
                            type: 'number',
                            width: '80px'
                        },
                        {
                            label: 'Date',
                            field: 'created_at',
                            sortable: true,
                            type: 'date',
                            width: '140px'
                        }
                    ],
                    rows: [
                        {
                            id: 1,
                            order_id: 'ORD-001',
                            customer: 'John Doe',
                            email: '<EMAIL>',
                            amount: 1250.00,
                            status: 'completed',
                            created_at: '2024-01-15T10:30:00Z',
                            items: 3
                        },
                        {
                            id: 2,
                            order_id: 'ORD-002',
                            customer: 'Jane Smith',
                            email: '<EMAIL>',
                            amount: 875.50,
                            status: 'pending',
                            created_at: '2024-01-14T14:20:00Z',
                            items: 2
                        },
                        {
                            id: 3,
                            order_id: 'ORD-003',
                            customer: 'Bob Johnson',
                            email: '<EMAIL>',
                            amount: 2100.75,
                            status: 'processing',
                            created_at: '2024-01-13T09:15:00Z',
                            items: 5
                        },
                        {
                            id: 4,
                            order_id: 'ORD-004',
                            customer: 'Alice Brown',
                            email: '<EMAIL>',
                            amount: 450.25,
                            status: 'cancelled',
                            created_at: '2024-01-12T16:45:00Z',
                            items: 1
                        },
                        {
                            id: 5,
                            order_id: 'ORD-005',
                            customer: 'Charlie Wilson',
                            email: '<EMAIL>',
                            amount: 1875.00,
                            status: 'completed',
                            created_at: '2024-01-11T11:30:00Z',
                            items: 4
                        }
                    ],
                    paginationOptions: {
                        enabled: true,
                        mode: 'records',
                        perPage: 10,
                        perPageDropdown: [5, 10, 15, 20],
                        dropdownAllowAll: false,
                        nextLabel: 'Next',
                        prevLabel: 'Previous',
                        rowsPerPageLabel: 'Rows per page',
                        ofLabel: 'of',
                        pageLabel: 'page'
                    },
                    searchOptions: {
                        enabled: true,
                        trigger: 'keyup',
                        placeholder: 'Search orders...'
                    },
                    sortOptions: {
                        enabled: true,
                        multipleColumns: false
                    }
                };
            },
            computed: {
                filteredRows() {
                    return this.rows;
                }
            },
            methods: {
                getInitials(name) {
                    return name
                        .split(' ')
                        .map(word => word.charAt(0))
                        .join('')
                        .toUpperCase();
                },
                formatDate(dateString) {
                    return new Date(dateString).toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric'
                    });
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
